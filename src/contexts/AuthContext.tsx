
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { enhancedToast } from "@/components/common/EnhancedToast";
import { userService, User } from "@/services";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Check if user is already logged in on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // With httpOnly cookies, we just need to call the endpoint
        // The cookies will be sent automatically with the request
        const userData = await userService.getCurrentUser();
        setUser(userData);
      } catch (error) {
        // If there's an error, the user is not authenticated
        console.error("Auth error:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);

      // The backend will set httpOnly cookies with the tokens
      const data = await userService.login(email, password);

      // Set user data from the response
      setUser(data.user);

      enhancedToast.success("Đăng nhập thành công!");

      // Role-based redirection
      if (data.user.role === "shop") {
        navigate("/shop/dashboard");
      } else if (data.user.role === "buyer") {
        navigate("/buyer/dashboard");
      } else {
        navigate("/dashboard");
      }
    } catch (error: any) {
      enhancedToast.error(error.message || "Email hoặc mật khẩu không đúng!");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Call the logout endpoint to clear the httpOnly cookies
      await userService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Clear user state regardless of API call success
      setUser(null);
      enhancedToast.info("Đã đăng xuất");
      navigate("/login");
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      setLoading(true);

      await userService.forgotPassword(email);

      enhancedToast.success(
        "Nếu email tồn tại trong hệ thống, bạn sẽ nhận được email hướng dẫn đặt lại mật khẩu"
      );
    } catch (error: any) {
      enhancedToast.error(error.message || "Đã xảy ra lỗi");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    try {
      setLoading(true);

      await userService.resetPassword(token, newPassword);

      enhancedToast.success("Mật khẩu đã được đặt lại thành công");
      navigate("/auth");
    } catch (error: any) {
      enhancedToast.error(error.message || "Đã xảy ra lỗi");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        forgotPassword,
        resetPassword,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
