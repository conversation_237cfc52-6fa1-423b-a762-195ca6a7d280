import { useState, ReactNode } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate, useLocation } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ArrowLeft, PhoneCall, MessageSquare, History, Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface CustomerLayoutProps {
  children: ReactNode;
}

interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
}

export function CustomerLayout({ children }: CustomerLayoutProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const userRole = user?.role || 'shop';
  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';

  const tabs: TabItem[] = [
    {
      id: 'call',
      label: 'Gọi CSKH',
      icon: <PhoneCall className="h-5 w-5" />,
      path: `${basePath}/call`
    },
    {
      id: 'support',
      label: 'Chat với CSKH',
      icon: <MessageSquare className="h-5 w-5" />,
      path: `${basePath}/support`
    },
    {
      id: 'history',
      label: 'Lịch sử đơn hàng',
      icon: <History className="h-5 w-5" />,
      path: `${basePath}/history`
    }
  ];

  const handleProfileClick = () => {
    navigate(`${basePath}/profile`);
  };

  const handleBackClick = () => {
    navigate(`${basePath}/dashboard`);
  };

  const isTabActive = (tabPath: string) => {
    return location.pathname === tabPath;
  };

  const isDashboard = location.pathname === `${basePath}/dashboard`;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-4">
          {/* Left side - Back button and Logo */}
          <div className="flex items-center gap-4">
            {!isDashboard && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBackClick}
                className="h-9 w-9"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            
            <div className="flex items-center gap-3">
              <div className="app-logo w-10 h-10">
                <img
                  src="/assets/images/logo/logo_ghvn.png"
                  alt="GHVN Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="font-semibold text-lg hidden sm:block text-foreground">GHVN CSKH</span>
            </div>
          </div>

          {/* Center - User info */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground hidden sm:block">
              Xin chào,
            </span>
            <span className="font-medium text-sm">
              {user?.fullName || 'Khách hàng'}
            </span>
          </div>

          {/* Right side - Menu */}
          <div className="flex items-center gap-2">
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>

            {/* Desktop user menu */}
            <DropdownMenu>
              <DropdownMenuTrigger className="hidden md:flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={`/api/files/content/${user?.avatarId}`} alt={user?.fullName} />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {user?.fullName
                      ? user.fullName
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                      : "U"}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Tài khoản</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={handleProfileClick}
                >
                  Thông tin cá nhân
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive cursor-pointer"
                  onClick={logout}
                >
                  Đăng xuất
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t bg-background">
            <div className="container py-4 px-4">
              <div className="flex flex-col gap-2">
                <Button
                  variant="ghost"
                  className="justify-start"
                  onClick={handleProfileClick}
                >
                  Thông tin cá nhân
                </Button>
                <Button
                  variant="ghost"
                  className="justify-start text-destructive hover:text-destructive"
                  onClick={logout}
                >
                  Đăng xuất
                </Button>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Tab Navigation - Only show on dashboard */}
      {isDashboard && (
        <div className="border-b bg-background">
          <div className="container px-4">
            <div className="grid grid-cols-3 gap-1 py-2">
              {tabs.map((tab) => (
                <Button
                  key={tab.id}
                  variant="ghost"
                  className={cn(
                    "flex flex-col items-center gap-2 h-auto py-4 px-2",
                    "hover:bg-muted/50 transition-colors",
                    isTabActive(tab.path) && "bg-muted"
                  )}
                  onClick={() => navigate(tab.path)}
                >
                  <div className="text-primary">
                    {tab.icon}
                  </div>
                  <span className="text-xs font-medium text-center leading-tight">
                    {tab.label}
                  </span>
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <main className="container px-4 py-6">
        {children}
      </main>
    </div>
  );
}
