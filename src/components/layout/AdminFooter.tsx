import { APP_CONFIG } from "@/config/env";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Shield, 
  Heart,
  ExternalLink
} from "lucide-react";

export function AdminFooter() {
  const currentYear = new Date().getFullYear();
  
  const contactInfo = [
    {
      icon: <Phone className="h-4 w-4" />,
      label: "Hotline",
      value: "1900-xxxx",
      href: "tel:1900xxxx"
    },
    {
      icon: <Mail className="h-4 w-4" />,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: <MapPin className="h-4 w-4" />,
      label: "Địa chỉ",
      value: "Hà Nội, Việt Nam",
      href: "#"
    },
    {
      icon: <Globe className="h-4 w-4" />,
      label: "Website",
      value: "www.ghvn.com",
      href: "https://www.ghvn.com"
    }
  ];

  const quickLinks = [
    { label: "Hướng dẫn sử dụng", href: "#" },
    { label: "Chính sách bảo mật", href: "#" },
    { label: "Điều khoản sử dụng", href: "#" },
    { label: "Hỗ trợ kỹ thuật", href: "#" }
  ];

  const systemInfo = [
    { label: "Phiên bản", value: "v2.1.0" },
    { label: "Cập nhật", value: "15/01/2025" },
    { label: "Trạng thái", value: "Hoạt động", status: "success" }
  ];

  return (
    <footer className="bg-muted/30 border-t border-border mt-auto">
      <div className="px-4 lg:px-8 py-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="app-logo w-10 h-10">
                <img
                  src={APP_CONFIG.LOGO}
                  alt="GHVN Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h3 className="font-semibold text-lg">GHVN</h3>
                <p className="text-sm text-muted-foreground">Hệ thống CSKH</p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Hệ thống quản trị chăm sóc khách hàng toàn diện, 
              cung cấp giải pháp tối ưu cho doanh nghiệp.
            </p>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">Bảo mật SSL</span>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="font-semibold">Thông tin liên hệ</h4>
            <div className="space-y-3">
              {contactInfo.map((contact, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="text-primary">{contact.icon}</div>
                  <div>
                    <p className="text-xs text-muted-foreground">{contact.label}</p>
                    <a 
                      href={contact.href}
                      className="text-sm hover:text-primary transition-colors"
                    >
                      {contact.value}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="font-semibold">Liên kết hữu ích</h4>
            <div className="space-y-2">
              {quickLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors group"
                >
                  {link.label}
                  <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                </a>
              ))}
            </div>
          </div>

          {/* System Info */}
          <div className="space-y-4">
            <h4 className="font-semibold">Thông tin hệ thống</h4>
            <div className="space-y-3">
              {systemInfo.map((info, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{info.label}:</span>
                  <div className="flex items-center gap-2">
                    {info.status ? (
                      <Badge variant="success" className="text-xs px-2 py-0">
                        {info.value}
                      </Badge>
                    ) : (
                      <span className="text-sm font-medium">{info.value}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Bottom Footer */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>COPYRIGHT © {currentYear} GHVN, All rights Reserved.</span>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Made with</span>
            <Heart className="h-4 w-4 text-red-500 fill-current" />
            <span>in Vietnam</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
