import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Upload, X, File, Image, FileText, Loader2 } from "lucide-react";

export interface FileInfo {
  /**
   * File object
   */
  file: File;
  
  /**
   * ID của file
   */
  id: string;
  
  /**
   * URL preview của file
   */
  preview?: string;
  
  /**
   * Trạng thái upload
   */
  status: "idle" | "uploading" | "success" | "error";
  
  /**
   * Tiến trình upload (0-100)
   */
  progress: number;
  
  /**
   * Thông báo lỗi
   */
  error?: string;
}

interface FileUploaderProps {
  /**
   * Danh sách file đã upload
   */
  files: FileInfo[];
  
  /**
   * Callback khi thêm file
   */
  onAddFiles: (files: File[]) => void;
  
  /**
   * Callback khi xóa file
   */
  onRemoveFile: (fileId: string) => void;
  
  /**
   * Loại file được phép upload
   * @default "*"
   */
  accept?: string;
  
  /**
   * <PERSON><PERSON> cho phép upload nhiều file không
   * @default false
   */
  multiple?: boolean;
  
  /**
   * Kích thước tối đa của file (bytes)
   */
  maxSize?: number;
  
  /**
   * Số lượng file tối đa
   */
  maxFiles?: number;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Text của nút upload
   * @default "Tải lên file"
   */
  buttonText?: string;
  
  /**
   * Mô tả
   */
  description?: string;
  
  /**
   * Có hiển thị preview không
   * @default true
   */
  showPreview?: boolean;
  
  /**
   * Có cho phép kéo thả không
   * @default true
   */
  allowDragDrop?: boolean;
  
  /**
   * Có disable không
   * @default false
   */
  disabled?: boolean;
}

export function FileUploader({
  files,
  onAddFiles,
  onRemoveFile,
  accept = "*",
  multiple = false,
  maxSize,
  maxFiles,
  className,
  buttonText = "Tải lên file",
  description,
  showPreview = true,
  allowDragDrop = true,
  disabled = false
}: FileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      
      // Kiểm tra số lượng file
      if (maxFiles && files.length + selectedFiles.length > maxFiles) {
        alert(`Bạn chỉ có thể tải lên tối đa ${maxFiles} file.`);
        return;
      }
      
      // Kiểm tra kích thước file
      if (maxSize) {
        const oversizedFiles = selectedFiles.filter(file => file.size > maxSize);
        if (oversizedFiles.length > 0) {
          alert(`Một số file vượt quá kích thước tối đa ${formatFileSize(maxSize)}.`);
          return;
        }
      }
      
      onAddFiles(selectedFiles);
      
      // Reset input để có thể chọn lại cùng một file
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };
  
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled && allowDragDrop) {
      setIsDragging(true);
    }
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled || !allowDragDrop) return;
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFiles = Array.from(e.dataTransfer.files);
      
      // Kiểm tra số lượng file
      if (maxFiles && files.length + droppedFiles.length > maxFiles) {
        alert(`Bạn chỉ có thể tải lên tối đa ${maxFiles} file.`);
        return;
      }
      
      // Kiểm tra kích thước file
      if (maxSize) {
        const oversizedFiles = droppedFiles.filter(file => file.size > maxSize);
        if (oversizedFiles.length > 0) {
          alert(`Một số file vượt quá kích thước tối đa ${formatFileSize(maxSize)}.`);
          return;
        }
      }
      
      onAddFiles(droppedFiles);
    }
  };
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  
  const getFileIcon = (file: File) => {
    const type = file.type;
    
    if (type.startsWith("image/")) {
      return <Image className="h-6 w-6 text-blue-500" />;
    } else if (type.includes("pdf")) {
      return <FileText className="h-6 w-6 text-red-500" />;
    } else if (type.includes("word") || type.includes("document")) {
      return <FileText className="h-6 w-6 text-blue-700" />;
    } else if (type.includes("excel") || type.includes("sheet")) {
      return <FileText className="h-6 w-6 text-green-600" />;
    } else {
      return <File className="h-6 w-6 text-gray-500" />;
    }
  };
  
  return (
    <div className={cn("space-y-4", className)}>
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center",
          isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/20",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
          className
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept={accept}
          multiple={multiple}
          className="hidden"
          disabled={disabled}
        />
        
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-10 w-10 text-muted-foreground" />
          <h3 className="text-lg font-medium">{buttonText}</h3>
          
          {description && (
            <p className="text-sm text-muted-foreground max-w-xs mx-auto">
              {description}
            </p>
          )}
          
          {(maxSize || maxFiles) && (
            <div className="text-xs text-muted-foreground mt-2">
              {maxSize && <p>Kích thước tối đa: {formatFileSize(maxSize)}</p>}
              {maxFiles && <p>Số lượng tối đa: {maxFiles} file</p>}
            </div>
          )}
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              if (!disabled) {
                fileInputRef.current?.click();
              }
            }}
            disabled={disabled}
          >
            Chọn file
          </Button>
        </div>
      </div>
      
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((fileInfo) => (
            <div
              key={fileInfo.id}
              className="flex items-center justify-between p-3 border rounded-md"
            >
              <div className="flex items-center space-x-3">
                {showPreview && fileInfo.file.type.startsWith("image/") && fileInfo.preview ? (
                  <div className="h-10 w-10 rounded-md overflow-hidden">
                    <img
                      src={fileInfo.preview}
                      alt={fileInfo.file.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                ) : (
                  getFileIcon(fileInfo.file)
                )}
                
                <div className="space-y-1">
                  <p className="text-sm font-medium truncate max-w-[200px]">
                    {fileInfo.file.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(fileInfo.file.size)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {fileInfo.status === "uploading" && (
                  <div className="w-24">
                    <Progress value={fileInfo.progress} className="h-2" />
                  </div>
                )}
                
                {fileInfo.status === "error" && (
                  <span className="text-xs text-destructive">
                    {fileInfo.error || "Lỗi tải lên"}
                  </span>
                )}
                
                {fileInfo.status === "uploading" ? (
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                ) : (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveFile(fileInfo.id);
                    }}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
