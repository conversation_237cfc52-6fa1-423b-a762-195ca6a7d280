import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Filter, X, CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

export type FilterType = "text" | "select" | "date" | "dateRange" | "number";

export interface FilterOption {
  value: string;
  label: string;
}

export interface FilterItem {
  /**
   * ID của filter
   */
  id: string;
  
  /**
   * Label của filter
   */
  label: string;
  
  /**
   * Loại filter
   */
  type: FilterType;
  
  /**
   * Giá trị hiện tại của filter
   */
  value?: string | string[] | number | Date | [Date | undefined, Date | undefined];
  
  /**
   * Placeholder của filter
   */
  placeholder?: string;
  
  /**
   * Các option cho filter loại select
   */
  options?: FilterOption[];
  
  /**
   * Có hiển thị filter không
   * @default true
   */
  show?: boolean;
}

interface FilterBarProps {
  /**
   * Danh sách các filter
   */
  filters: FilterItem[];
  
  /**
   * Callback khi thay đổi giá trị filter
   */
  onChange: (id: string, value: any) => void;
  
  /**
   * Callback khi reset tất cả filter
   */
  onReset?: () => void;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Có hiển thị nút reset không
   * @default true
   */
  showResetButton?: boolean;
}

export function FilterBar({
  filters,
  onChange,
  onReset,
  className,
  showResetButton = true
}: FilterBarProps) {
  // Lọc các filter có show = true hoặc không có thuộc tính show
  const visibleFilters = filters.filter(filter => filter.show !== false);
  
  // Nếu không có filter nào hiển thị, không render component
  if (visibleFilters.length === 0) {
    return null;
  }
  
  // Kiểm tra xem có filter nào đang được áp dụng không
  const hasActiveFilters = visibleFilters.some(filter => {
    if (Array.isArray(filter.value)) {
      return filter.value.some(v => v !== undefined);
    }
    return filter.value !== undefined && filter.value !== "" && filter.value !== null;
  });
  
  const renderFilterInput = (filter: FilterItem) => {
    switch (filter.type) {
      case "text":
        return (
          <Input
            placeholder={filter.placeholder || `Nhập ${filter.label.toLowerCase()}`}
            value={filter.value as string || ""}
            onChange={(e) => onChange(filter.id, e.target.value)}
            className="w-full"
          />
        );
        
      case "select":
        return (
          <Select
            value={filter.value as string}
            onValueChange={(value) => onChange(filter.id, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={filter.placeholder || `Chọn ${filter.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {filter.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
        
      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !filter.value && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {filter.value ? (
                  format(filter.value as Date, "dd/MM/yyyy", { locale: vi })
                ) : (
                  <span>{filter.placeholder || `Chọn ${filter.label.toLowerCase()}`}</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={filter.value as Date}
                onSelect={(date) => onChange(filter.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );
        
      case "number":
        return (
          <Input
            type="number"
            placeholder={filter.placeholder || `Nhập ${filter.label.toLowerCase()}`}
            value={filter.value as number || ""}
            onChange={(e) => onChange(filter.id, e.target.valueAsNumber)}
            className="w-full"
          />
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Bộ lọc</span>
        </div>
        
        {showResetButton && hasActiveFilters && onReset && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onReset}
            className="h-8 px-2 text-xs"
          >
            <X className="mr-1 h-3 w-3" />
            Xóa bộ lọc
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {visibleFilters.map((filter) => (
          <div key={filter.id} className="space-y-1">
            <label className="text-sm font-medium">{filter.label}</label>
            {renderFilterInput(filter)}
          </div>
        ))}
      </div>
    </div>
  );
}
