import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";

export interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationProps {
  /**
   * Thông tin phân trang
   */
  paginationInfo: PaginationInfo;
  
  /**
   * Callback khi thay đổi trang
   */
  onPageChange: (page: number) => void;
  
  /**
   * Tên của các mục đang hiển thị (ví dụ: "quản trị viên", "sản phẩm", v.v.)
   * @default "mục"
   */
  itemName?: string;
  
  /**
   * Hiển thị thông tin về số lượng mục đang hiển thị
   * @default true
   */
  showItemCount?: boolean;
  
  /**
   * Hiển thị nút nhảy đến trang đầu/cuối
   * @default false
   */
  showFirstLastButtons?: boolean;
  
  /**
   * Số trang hiển thị xung quanh trang hiện tại
   * @default 1
   */
  siblingCount?: number;
}

export function Pagination({
  paginationInfo,
  onPageChange,
  itemName = "mục",
  showItemCount = true,
  showFirstLastButtons = false,
  siblingCount = 1
}: PaginationProps) {
  const { total, page, limit, totalPages } = paginationInfo;
  
  // Tính toán số mục đang hiển thị trên trang hiện tại
  const currentDisplayCount = Math.min(limit, total - (page - 1) * limit);
  
  // Tạo mảng các trang để hiển thị
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // Luôn hiển thị trang đầu tiên
    pageNumbers.push(1);
    
    // Tính toán phạm vi trang cần hiển thị
    const leftSibling = Math.max(2, page - siblingCount);
    const rightSibling = Math.min(totalPages - 1, page + siblingCount);
    
    // Thêm dấu ... nếu cần
    if (leftSibling > 2) {
      pageNumbers.push('...');
    }
    
    // Thêm các trang ở giữa
    for (let i = leftSibling; i <= rightSibling; i++) {
      if (i !== 1 && i !== totalPages) {
        pageNumbers.push(i);
      }
    }
    
    // Thêm dấu ... nếu cần
    if (rightSibling < totalPages - 1) {
      pageNumbers.push('...');
    }
    
    // Luôn hiển thị trang cuối cùng nếu có nhiều hơn 1 trang
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };
  
  // Không hiển thị phân trang nếu không có dữ liệu hoặc chỉ có 1 trang
  if (total === 0 || totalPages <= 1) {
    return null;
  }
  
  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-4">
      {showItemCount && (
        <div className="text-sm text-muted-foreground">
          Hiển thị {currentDisplayCount} trên tổng số {total} {itemName}
        </div>
      )}
      
      <div className="flex items-center space-x-2">
        {showFirstLastButtons && (
          <Button
            variant="outline"
            size="icon"
            onClick={() => onPageChange(1)}
            disabled={page <= 1}
            title="Trang đầu"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
        )}
        
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(Math.max(1, page - 1))}
          disabled={page <= 1}
          title="Trang trước"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        {/* Hiển thị các số trang */}
        <div className="hidden sm:flex items-center space-x-1">
          {getPageNumbers().map((pageNumber, index) => (
            typeof pageNumber === 'number' ? (
              <Button
                key={index}
                variant={pageNumber === page ? "default" : "outline"}
                size="icon"
                className="w-8 h-8"
                onClick={() => onPageChange(pageNumber)}
                disabled={pageNumber === page}
              >
                {pageNumber}
              </Button>
            ) : (
              <span key={index} className="px-2">
                {pageNumber}
              </span>
            )
          ))}
        </div>
        
        {/* Hiển thị thông tin trang trên mobile */}
        <div className="sm:hidden text-sm">
          Trang {page} / {totalPages}
        </div>
        
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(Math.min(totalPages, page + 1))}
          disabled={page >= totalPages}
          title="Trang sau"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        
        {showFirstLastButtons && (
          <Button
            variant="outline"
            size="icon"
            onClick={() => onPageChange(totalPages)}
            disabled={page >= totalPages}
            title="Trang cuối"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
