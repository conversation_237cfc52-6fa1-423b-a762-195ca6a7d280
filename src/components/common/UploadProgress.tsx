import React from "react";
import { Progress } from "@/components/ui/progress";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface UploadProgressProps {
  /**
   * Tiến trình upload (0-100)
   */
  progress: number;
  
  /**
   * Trạng thái upload
   */
  status: "idle" | "uploading" | "success" | "error";
  
  /**
   * Thông báo lỗi
   */
  error?: string;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Hiển thị text phần trăm
   * @default true
   */
  showPercentage?: boolean;
  
  /**
   * <PERSON><PERSON>ch thước của progress bar
   * @default "md"
   */
  size?: "sm" | "md" | "lg";
}

/**
 * Component hiển thị tiến trình upload
 */
export function UploadProgress({
  progress,
  status,
  error,
  className,
  showPercentage = true,
  size = "md"
}: UploadProgressProps) {
  // <PERSON><PERSON><PERSON> đ<PERSON>nh kích thước của progress bar
  const progressHeight = {
    sm: "h-1",
    md: "h-2",
    lg: "h-3"
  }[size];
  
  return (
    <div className={cn("flex flex-col gap-1", className)}>
      <div className="flex items-center gap-2">
        <div className="flex-1">
          <Progress 
            value={progress} 
            className={cn(
              progressHeight,
              status === "error" && "bg-red-100 [&>div]:bg-red-500",
              status === "success" && "bg-green-100 [&>div]:bg-green-500"
            )} 
          />
        </div>
        
        {showPercentage && (
          <span className="text-xs font-medium">
            {status === "uploading" ? (
              <span className="flex items-center gap-1">
                {Math.round(progress)}%
                <Loader2 className="h-3 w-3 animate-spin" />
              </span>
            ) : status === "success" ? (
              <span className="text-green-600">100%</span>
            ) : status === "error" ? (
              <span className="text-red-600">Lỗi</span>
            ) : (
              <span>{Math.round(progress)}%</span>
            )}
          </span>
        )}
      </div>
      
      {status === "error" && error && (
        <p className="text-xs text-red-600">{error}</p>
      )}
    </div>
  );
}
