import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface FormDialogProps {
  /**
   * Trạng thái mở của dialog
   */
  open: boolean;
  
  /**
   * Callback khi thay đổi trạng thái mở
   */
  onOpenChange: (open: boolean) => void;
  
  /**
   * Tiêu đề của dialog
   */
  title: string;
  
  /**
   * Mô tả của dialog
   */
  description?: string;
  
  /**
   * Nội dung form
   */
  children: React.ReactNode;
  
  /**
   * <PERSON>ang submit form
   */
  isSubmitting?: boolean;
  
  /**
   * Text của nút submit
   * @default "Lưu"
   */
  submitText?: string;
  
  /**
   * Text của nút hủy
   * @default "Hủy"
   */
  cancelText?: string;
  
  /**
   * Text khi đang submit
   * @default "<PERSON><PERSON> lưu..."
   */
  submittingText?: string;
  
  /**
   * Callback khi submit form
   */
  onSubmit?: () => void;
  
  /**
   * <PERSON><PERSON>ch thước tối đa của dialog
   * @default "md"
   */
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "full";
  
  /**
   * Có hiển thị footer không
   * @default true
   */
  showFooter?: boolean;
  
  /**
   * Có hiển thị nút hủy không
   * @default true
   */
  showCancelButton?: boolean;
  
  /**
   * Có hiển thị nút submit không
   * @default true
   */
  showSubmitButton?: boolean;
  
  /**
   * Nội dung footer tùy chỉnh
   */
  footerContent?: React.ReactNode;
}

const maxWidthClasses = {
  sm: "sm:max-w-sm",
  md: "sm:max-w-md",
  lg: "sm:max-w-lg",
  xl: "sm:max-w-xl",
  "2xl": "sm:max-w-2xl",
  "3xl": "sm:max-w-3xl",
  "4xl": "sm:max-w-4xl",
  "5xl": "sm:max-w-5xl",
  "full": "sm:max-w-full"
};

export function FormDialog({
  open,
  onOpenChange,
  title,
  description,
  children,
  isSubmitting = false,
  submitText = "Lưu",
  cancelText = "Hủy",
  submittingText = "Đang lưu...",
  onSubmit,
  maxWidth = "md",
  showFooter = true,
  showCancelButton = true,
  showSubmitButton = true,
  footerContent
}: FormDialogProps) {
  const handleSubmit = (e: React.FormEvent) => {
    if (onSubmit) {
      e.preventDefault();
      onSubmit();
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={maxWidthClasses[maxWidth]}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && (
            <DialogDescription>
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        {onSubmit ? (
          <form onSubmit={handleSubmit}>
            <div className="py-4">
              {children}
            </div>
            
            {showFooter && (
              <DialogFooter className="mt-6">
                {footerContent}
                
                {showCancelButton && (
                  <DialogClose asChild>
                    <Button type="button" variant="outline">{cancelText}</Button>
                  </DialogClose>
                )}
                
                {showSubmitButton && (
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {isSubmitting ? submittingText : submitText}
                  </Button>
                )}
              </DialogFooter>
            )}
          </form>
        ) : (
          <>
            <div className="py-4">
              {children}
            </div>
            
            {showFooter && (
              <DialogFooter className="mt-6">
                {footerContent}
                
                {showCancelButton && (
                  <DialogClose asChild>
                    <Button type="button" variant="outline">{cancelText}</Button>
                  </DialogClose>
                )}
                
                {showSubmitButton && (
                  <Button type="button" disabled={isSubmitting} onClick={() => onSubmit && onSubmit()}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {isSubmitting ? submittingText : submitText}
                  </Button>
                )}
              </DialogFooter>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
