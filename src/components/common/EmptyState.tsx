import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface EmptyStateProps {
  /**
   * Tiêu đề của trạng thái trống
   */
  title: string;
  
  /**
   * <PERSON><PERSON> tả của trạng thái trống
   */
  description?: string;
  
  /**
   * Icon hiển thị
   */
  icon?: React.ReactNode;
  
  /**
   * Text của nút hành động
   */
  actionText?: string;
  
  /**
   * Callback khi click vào nút hành động
   */
  onAction?: () => void;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Nội dung bổ sung
   */
  children?: React.ReactNode;
  
  /**
   * Kích thước của component
   * @default "default"
   */
  size?: "sm" | "default" | "lg";
  
  /**
   * Có hiển thị border không
   * @default false
   */
  withBorder?: boolean;
}

export function EmptyState({
  title,
  description,
  icon,
  actionText,
  onAction,
  className,
  children,
  size = "default",
  withBorder = false
}: EmptyStateProps) {
  const sizeClasses = {
    sm: "p-4 space-y-2",
    default: "p-6 space-y-4",
    lg: "p-8 space-y-6"
  };
  
  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center text-center rounded-lg bg-background",
        sizeClasses[size],
        withBorder && "border",
        className
      )}
    >
      {icon && (
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          {icon}
        </div>
      )}
      
      <div className="space-y-2">
        <h3 className="text-lg font-medium">{title}</h3>
        
        {description && (
          <p className="text-sm text-muted-foreground max-w-md mx-auto">
            {description}
          </p>
        )}
      </div>
      
      {children}
      
      {actionText && onAction && (
        <Button onClick={onAction} className="mt-4">
          {actionText}
        </Button>
      )}
    </div>
  );
}
