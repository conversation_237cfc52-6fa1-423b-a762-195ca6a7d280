import React from "react";
import { cn } from "@/lib/utils";

type StatusVariant = 
  | "success" 
  | "warning" 
  | "error" 
  | "info" 
  | "default" 
  | "secondary" 
  | "outline";

interface StatusBadgeProps {
  /**
   * <PERSON>ội dung hiển thị
   */
  children: React.ReactNode;
  
  /**
   * Variant của badge
   * @default "default"
   */
  variant?: StatusVariant;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * <PERSON><PERSON>ch thước của badge
   * @default "md"
   */
  size?: "sm" | "md" | "lg";
}

const variantStyles: Record<StatusVariant, string> = {
  success: "bg-green-100 text-green-800",
  warning: "bg-yellow-100 text-yellow-800",
  error: "bg-red-100 text-red-800",
  info: "bg-blue-100 text-blue-800",
  default: "bg-gray-100 text-gray-800",
  secondary: "bg-purple-100 text-purple-800",
  outline: "bg-transparent border border-gray-200 text-gray-800"
};

const sizeStyles = {
  sm: "px-1.5 py-0.5 text-xs",
  md: "px-2 py-1 text-xs",
  lg: "px-2.5 py-1.5 text-sm"
};

export function StatusBadge({
  children,
  variant = "default",
  className = "",
  size = "md"
}: StatusBadgeProps) {
  return (
    <span 
      className={cn(
        "inline-flex items-center justify-center font-medium rounded-full whitespace-nowrap",
        variantStyles[variant],
        sizeStyles[size],
        className
      )}
    >
      {children}
    </span>
  );
}

// Các preset phổ biến
export function ActiveStatusBadge({ className = "", size = "md" }: Omit<StatusBadgeProps, "children" | "variant">) {
  return (
    <StatusBadge variant="success" className={className} size={size}>
      Hoạt động
    </StatusBadge>
  );
}

export function InactiveStatusBadge({ className = "", size = "md" }: Omit<StatusBadgeProps, "children" | "variant">) {
  return (
    <StatusBadge variant="default" className={className} size={size}>
      Không hoạt động
    </StatusBadge>
  );
}

export function PendingStatusBadge({ className = "", size = "md" }: Omit<StatusBadgeProps, "children" | "variant">) {
  return (
    <StatusBadge variant="warning" className={className} size={size}>
      Đang chờ
    </StatusBadge>
  );
}

export function RejectedStatusBadge({ className = "", size = "md" }: Omit<StatusBadgeProps, "children" | "variant">) {
  return (
    <StatusBadge variant="error" className={className} size={size}>
      Từ chối
    </StatusBadge>
  );
}

export function ApprovedStatusBadge({ className = "", size = "md" }: Omit<StatusBadgeProps, "children" | "variant">) {
  return (
    <StatusBadge variant="success" className={className} size={size}>
      Đã duyệt
    </StatusBadge>
  );
}
