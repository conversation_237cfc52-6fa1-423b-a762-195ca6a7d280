import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Phone, Video, MoreVertical, User } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Conversation } from '@/services/CskhService';
import {useAuth} from "@/contexts/AuthContext";

interface ChatHeaderProps {
  conversation: Conversation | null;
  onCall?: () => void;
  onVideoCall?: () => void;
  onViewProfile?: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  conversation,
  onCall,
  onVideoCall,
  onViewProfile,
}) => {
  const {user} = useAuth();

  if (!conversation) {
    return (
      <div className="h-16 border-b border-border bg-card flex items-center justify-center">
        <p className="text-muted-foreground"><PERSON><PERSON><PERSON> một cuộc trò chuyện để bắt đầu</p>
      </div>
    );
  }

  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="h-16 border-b border-border bg-card flex items-center justify-between px-4">
      {/* User Info */}
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage
            src={conversation.ownerUserId ? `/api/files/content/${conversation.ownerUserId}` : undefined}
            alt={conversation.title}
          />
          <AvatarFallback className="bg-primary/10 text-primary">
            {getInitials(conversation.title)}
          </AvatarFallback>
        </Avatar>

        <div>
          <h3 className="font-semibold text-sm">{conversation.title}</h3>
          <div className="flex items-center gap-2">
            {/*<Badge variant="outline" className="text-xs">*/}
            {/*  {conversation.customerType === 'buyer' ? 'Người mua' : 'Cửa hàng'}*/}
            {/*</Badge>*/}
            <Badge variant={conversation.status === 'open' ? 'default' : 'secondary'} className="text-xs">
              {conversation.status === 'open' ? 'Đang mở' : 'Đã đóng'}
            </Badge>
            {conversation.unreadCount !== undefined && conversation.unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {conversation.unreadCount} tin nhắn mới
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {onCall && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onCall}
            title="Gọi điện"
          >
            <Phone className="h-4 w-4" />
          </Button>
        )}

        {onVideoCall && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onVideoCall}
            title="Gọi video"
          >
            <Video className="h-4 w-4" />
          </Button>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onViewProfile && (
              <DropdownMenuItem onClick={onViewProfile}>
                <User className="h-4 w-4 mr-2" />
                Xem thông tin khách hàng
              </DropdownMenuItem>
            )}
            <DropdownMenuItem>
              <Phone className="h-4 w-4 mr-2" />
              Lịch sử cuộc gọi
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
