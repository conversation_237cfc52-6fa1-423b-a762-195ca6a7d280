import React, {useEffect, useRef} from 'react';
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Badge} from '@/components/ui/badge';
import {MessageSquare, Clock, CheckCheck} from 'lucide-react';
import {cn, formatTime, formatDate} from '@/lib/utils';
import {Message, Conversation, Sender} from '@/services/CskhService.ts';
import MessageGroup from './MessageGroup'
import {useAuth} from "@/contexts/AuthContext.tsx";

interface ChatContentProps {
  messages: Message[];
  currentUserId: string;
  conversation: Conversation | null;
  loading?: boolean;
}

export const ChatContent: React.FC<ChatContentProps> = ({
                                                          messages,
                                                          roomInfo,
                                                          currentUserId,
                                                          conversation,
                                                          loading = false,
                                                        }) => {
  const {user} = useAuth();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isFirstRender = useRef(true);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({
      behavior: 'auto',
    });
    isFirstRender.current = false;
  }, [messages]);


  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const unread = messages.filter(msg => !msg.read && msg.senderId !== user._id);
    const read = messages.filter(msg => msg.read || msg.senderId === user._id);
    const groups: { [key: string]: Message[] } = {};

    read.forEach(message => {
      const dateKey = new Date(message.createdAt).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    const result = Object.entries(groups).map(([dateKey, messages]) => ({
      groupName: formatDate(dateKey),
      messages
    }));

    if (unread.length > 0)
      result.push({groupName: 'Tin nhắn mới', messages: unread})

    return result
  };


  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-muted-foreground">Đang tải tin nhắn...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50"/>
          <h3 className="font-medium mb-2">Chưa có tin nhắn nào</h3>
          <p className="text-muted-foreground text-sm">
            Bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên
          </p>
        </div>
      </div>
    );
  }

  const messageGroups = groupMessagesByDate(messages);

  return (
    <ScrollArea className="flex-1" ref={scrollAreaRef}>
      <div className="p-4 space-y-4">
        {messageGroups.map(({groupName, messages}, index) => (
          <MessageGroup
            key={index}
            messages={messages}
            userId={user._id}
            groupName={groupName}
            conversation={conversation}
          />
        ))}
        <div ref={messagesEndRef}/>
      </div>
    </ScrollArea>
  );
};
