import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {useDebouncedSearch} from '@/hooks/use-debounced-search'
import {MessageAttachment} from '@/services/ChatService';
import {Conversation, cskhService, Message} from '@/services/CskhService';
import {io} from "socket.io-client";
import {ChatSidebar} from './components/ChatSidebar';
import {ChatHeader} from './components/ChatHeader';
import {ChatContent} from './components/ChatContent';
import {ChatInput} from './components/ChatInput';
import {toast} from '@/hooks/use-toast';

export const ChatPage: React.FC = () => {
  const {user} = useAuth();
  const socket = useRef();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [roomInfo, setRoomInfo] = useState<Conversation>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);


  // Initialize socket connection
  useEffect(() => {
    socket.current = io("/chat-cskh", {
      path: "/socket.io/socket.io",
      transports: ["websocket"],
      query: {
        verify: true,
      },
    });

    socket.current.on("connect", () => {
      setSocketConnected(true);
      socket.current.emit("join_workspace", {user_id: user._id});
    });

    socket.current.on("disconnect", () => {
      setSocketConnected(false);
    });


    socket.current.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      });
    });

    socket.current.on("notify_conversation", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
    });

    socket.current.on("read_message", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
      setMessages(data.messages);
    });

    return () => {
      socket.current.disconnect();
    };

  }, []);


  useEffect(() => {
    fetchConversations();
  }, []);


  const fetchConversations = async () => {
    try {
      setLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 50,
        userId: user._id,
        title: searchQuery,
      };

      const result = await cskhService.getConversations(params);
      if (result) setConversations(result.data);

    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách cuộc trò chuyện",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchMessages = async (conversationId) => {
    try {
      setMessagesLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 100,
      };

      const result = await cskhService.getMessages(conversationId, params);
      if (result) {
        setMessages(result.data.messages);
      }

    } catch (error) {
      console.error('Error fetching message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải nội dung tin nhắn",
        variant: "destructive",
      });
    } finally {
      setMessagesLoading(false);
    }
  };


  const serchConversations = useCallback(() => {
    fetchConversations()
  }, [searchQuery]);

  useDebouncedSearch(searchQuery, 500, serchConversations);

  const handleSelectConversation = async (conversation: Conversation) => {
    if (activeConversationId === conversation._id) return;
    if (activeConversationId) socket.current.emit("leave_room", {room_id: activeConversationId});

    socket.current.emit("join_room", {room_id: conversation._id});
    setActiveConversationId(conversation._id);
    if (conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
    else
      await fetchMessages(conversation._id)

  };

  const handleFocus = async () => {
    const activeConversation = conversations?.find(conv => conv._id === activeConversationId);
    if (activeConversation.unread > 0)
      await cskhService.markReadMessage(activeConversationId);
  }

  const handleSendMessage = async (content: string, attachments: MessageAttachment[] = []) => {
    if (!activeConversationId || !user) return;

    try {

      await cskhService.sendMessages(activeConversationId, {text: content})

    } catch (error) {
      console.error('❌ Error sending message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn",
        variant: "destructive",
      });
    }
  };

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">
      {/* Sidebar */}
      <ChatSidebar
        conversations={conversations}
        activeConversationId={activeConversationId}
        onSelectConversation={handleSelectConversation}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        loading={loading}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <ChatHeader
          conversation={activeConversation || null}
          onCall={() => {
            // TODO: Integrate with call center
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng gọi điện sẽ được tích hợp với call center",
            });
          }}
          onVideoCall={() => {
            // TODO: Implement video call
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng gọi video sẽ được phát triển trong tương lai",
            });
          }}
          onViewProfile={() => {
            // TODO: Navigate to customer profile
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng xem thông tin khách hàng sẽ được tích hợp",
            });
          }}
        />

        {/* Messages */}
        <ChatContent
          messages={messages}
          currentUserId={user?._id || ''}
          conversation={activeConversation || null}
          loading={messagesLoading}
        />

        {/* Input */}
        {activeConversationId && (
          <ChatInput
            handleFocus={handleFocus}
            onSendMessage={handleSendMessage}
            disabled={!activeConversationId}
            placeholder={socketConnected ? "Nhập tin nhắn..." : "Nhập tin nhắn (offline)..."}
          />
        )}

        {/* Socket status indicator */}
        {!socketConnected && (
          <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200 text-yellow-800 text-sm">
            ⚠️ Chế độ offline - Tin nhắn sẽ được gửi nhưng không có real-time updates
          </div>
        )}
      </div>
    </div>
  );
};
