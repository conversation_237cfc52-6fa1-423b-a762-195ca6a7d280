import { useState, useEffect } from "react";
import { DefaultLayout } from "@/components/layout/DefaultLayout";
import { Phone, Users, MessageSquare, CheckCircle } from "lucide-react";
import { SEO } from "@/components/SEO";

// Import common components
import { PageHeader } from "@/components/common/PageHeader";
import { InfoCard } from "@/components/common/InfoCard";

interface DashboardMetric {
  id: string;
  title: string;
  value: number | string;
  description: string;
  icon: React.ReactNode;
  colorClass: string;
}

export default function Dashboard() {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<DashboardMetric[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // In a real app, this would be an API call
        // await axios.get('http://localhost:3000/api/dashboard/summary')

        // For now, we'll simulate a delay and use mock data
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setMetrics([
          {
            id: "total-calls",
            title: "Tổng cuộc gọi",
            value: "1,250",
            description: "Tất cả các cuộc gọi",
            icon: <Phone className="h-5 w-5" />,
            colorClass: "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
          },
          {
            id: "total-customers",
            title: "Tổng khách hàng",
            value: "856",
            description: "Đã đăng ký",
            icon: <Users className="h-5 w-5" />,
            colorClass: "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400",
          },
          {
            id: "online-admins",
            title: "Quản trị viên trực tuyến",
            value: "8",
            description: "Trong tổng số 22",
            icon: <Users className="h-5 w-5" />,
            colorClass: "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
          },
          {
            id: "active-chats",
            title: "Cuộc trò chuyện đang hoạt động",
            value: "32",
            description: "Cuộc hội thoại đang diễn ra",
            icon: <MessageSquare className="h-5 w-5" />,
            colorClass: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400",
          },
          {
            id: "active-calls",
            title: "Cuộc gọi đang hoạt động",
            value: "15",
            description: "Đang tiến hành",
            icon: <Phone className="h-5 w-5" />,
            colorClass: "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400",
          },
          {
            id: "resolved-calls",
            title: "Cuộc gọi đã giải quyết",
            value: "1,150",
            description: "Đã hoàn thành",
            icon: <CheckCircle className="h-5 w-5" />,
            colorClass: "bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400",
          },
        ]);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <DefaultLayout>
      <SEO title="Dashboard" description="Tổng quan về hoạt động chăm sóc khách hàng" />
      <div className="space-y-6">
        <PageHeader
          title="Dashboard"
          description="Tổng quan về hoạt động chăm sóc khách hàng"
        />

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {loading
            ? Array(6)
                .fill(null)
                .map((_, i) => (
                  <InfoCard
                    key={i}
                    title=""
                    value=""
                    loading={true}
                    withBorder={true}
                  />
                ))
            : metrics.map((metric) => (
                <InfoCard
                  key={metric.id}
                  title={metric.title}
                  value={metric.value}
                  icon={metric.icon}
                  description={metric.description}
                  withBorder={true}
                  withHoverEffect={true}
                />
              ))}
        </div>
      </div>
    </DefaultLayout>
  );
}
