import React, {useRef, useEffect, useState} from "react";
import {Avatar, AvatarFallback} from "@/components/ui/avatar.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {Message} from "@/services/CskhService.ts";
import {cn, formatTime, formatDate} from '@/lib/utils';
import {CheckCheck} from "lucide-react";

interface ChatContentProps {
  groupName: string
  messages: Message[]
  userId: string
}

const MessageGroup: React.FC<ChatContentProps> = ({
                                                    groupName,
                                                    messages,
                                                    userId,
                                                  }) => {

  return (<div>
    <div className="flex items-center justify-center my-4">
      <Badge variant="outline" className="bg-slate-800 text-slate-300 border-slate-600 text-xs">
        {groupName}
      </Badge>
    </div>

    {/* Messages */}
    {messages.map((msg) => {
      const isSender = userId === msg.senderId
      return <div key={msg._id}
                  className={`flex gap-3 my-2 ${isSender ? 'justify-end' : 'justify-start'}`}
      >
        <div
          className={`max-w-[70%] px-3 py-2 ${
            isSender
              ? 'bg-purple-600 text-white rounded-l-xl rounded-tr-xl'
              : 'bg-slate-700 text-slate-100 rounded-r-xl rounded-tl-xl'
          }`}
        >
          <p className={cn("text-sm whitespace-pre-wrap break-words",
            (!isSender && !msg.read) && "font-medium"
          )}>
            {msg.text}
          </p>

          <div
            className="flex items-center gap-1 mt-1"
          >
            <span className={`text-xs ${
              isSender ? 'text-purple-200' : 'text-slate-400'
            }`}>
             {formatTime(msg.createdAt)}
            </span>

            {msg.read && isSender && (
              <span className="text-xs h-4 px-1">
                <CheckCheck size={16}/>
              </span>
            )}
          </div>

        </div>
      </div>
    })}
  </div>)

}

export default MessageGroup;