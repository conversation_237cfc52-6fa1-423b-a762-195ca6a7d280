import React from "react";
import {Avatar, AvatarFallback} from "@/components/ui/avatar.tsx";
import {Conversation} from "@/services/CskhService.ts";
import {X} from "lucide-react";

interface ChatHeaderProps {
  setIsOpen: (open: boolean) => void;
  conversation: Conversation;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 setIsOpen,
                                                 conversation,
                                               }) => {


  return (<div
      className="flex justify-between items-center p-4 border-b border-slate-700 bg-slate-800 text-white rounded-t-lg">
      <div className="flex items-center gap-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="bg-purple-600 text-white text-xs">
            SP
          </AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-lg font-semibold">Hỗ trợ trực tuyến</h2>
          {conversation && <p className="text-xs text-slate-300">
            {conversation.status === 'open' ? <>
              <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"/>
              <PERSON>ang hoạt động
            </> : <>
              <span className="inline-block w-2 h-2 rounded-full bg-yellow-400 mr-1"/>
              Đang kết nối
            </>}
          </p>}
        </div>
      </div>
      <button
        onClick={() => setIsOpen(false)}
        className="hover:bg-slate-700 p-1 rounded transition-colors"
      >
        <X size={20}/>
      </button>
    </div>
  )

}

export default ChatHeader;