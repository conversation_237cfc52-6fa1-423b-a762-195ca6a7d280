import React, { useState, useEffect } from 'react';
import { useSipCall } from '../hooks/useSipCall';
import { CallStatus } from '../services/sipService';
import { SIP_CONFIG } from '../config/sipConfig';
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import {
  PhoneCall,
  Phone,
  PhoneIncoming,
  PhoneOff,
  PhoneOutgoing,
  Mic,
  MicOff,
  Disc,
  RefreshCw,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CallInterfaceProps {
    username?: string;
    password?: string;
}

const CallInterface: React.FC<CallInterfaceProps> = ({
    username = SIP_CONFIG.DEFAULT_USER,
    password = SIP_CONFIG.DEFAULT_PASSWORD
}) => {
    const [destination, setDestination] = useState('');
    const [callDuration, setCallDuration] = useState(0);
    const [timerInterval, setTimerInterval] = useState<NodeJS.Timeout | null>(null);

    const {
        callState,
        isInitialized,
        isMuted,
        initialize,
        makeCall,
        answerCall,
        rejectCall,
        hangupCall,
        startRecording,
        stopRecording,
        toggleMute
    } = useSipCall({ autoConnect: false });

    // Xử lý khi component mount
    useEffect(() => {
        // Khởi tạo SIP service
        initialize(username, password);

        // Cleanup khi component unmount
        return () => {
            if (timerInterval) {
                clearInterval(timerInterval);
            }
        };
    }, [initialize, username, password]);

    // Xử lý timer cho thời gian cuộc gọi
    useEffect(() => {
        if (callState.status === CallStatus.CONNECTED) {
            // Bắt đầu đếm thời gian
            const interval = setInterval(() => {
                setCallDuration(prev => prev + 1);
            }, 1000);

            setTimerInterval(interval);

            return () => {
                clearInterval(interval);
            };
        } else if (callState.status === CallStatus.DISCONNECTED || callState.status === CallStatus.IDLE) {
            // Dừng đếm thời gian
            if (timerInterval) {
                clearInterval(timerInterval);
                setTimerInterval(null);
            }

            // Reset thời gian nếu trạng thái là IDLE
            if (callState.status === CallStatus.IDLE) {
                setCallDuration(0);
            }
        }
    }, [callState.status]);

    // Format thời gian cuộc gọi
    const formatDuration = (seconds: number): string => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // Xử lý khi nhấn nút gọi
    const handleCall = async (options?: { skipMediaAccess?: boolean }) => {
        if (!destination) return;

        try {
            await makeCall(destination, options);
        } catch (error) {
            console.error('Failed to make call', error);
        }
    };

    // Xử lý khi nhấn nút trả lời
    const handleAnswer = async (options?: { skipMediaAccess?: boolean }) => {
        try {
            await answerCall(options);
        } catch (error) {
            console.error('Failed to answer call', error);
        }
    };

    // Xử lý khi nhấn nút từ chối
    const handleReject = async () => {
        try {
            await rejectCall();
        } catch (error) {
            console.error('Failed to reject call', error);
        }
    };

    // Xử lý khi nhấn nút kết thúc cuộc gọi
    const handleHangup = async () => {
        try {
            await hangupCall();
        } catch (error) {
            console.error('Failed to hangup call', error);
        }
    };

    // Xử lý khi nhấn nút ghi âm
    const handleRecording = () => {
        if (callState.recording) {
            stopRecording();
        } else {
            startRecording();
        }
    };

    // Xử lý khi nhấn nút tắt/bật microphone
    const handleToggleMute = () => {
        toggleMute();
    };

    // Render trạng thái cuộc gọi
    const renderCallStatus = () => {
        switch (callState.status) {
            case CallStatus.IDLE:
                return <Badge variant="secondary" className="flex items-center gap-1">
                    <CheckCircle2 className="h-3 w-3" />
                    Sẵn sàng
                </Badge>;
            case CallStatus.CONNECTING:
                return <Badge variant="secondary" className="flex items-center gap-1 bg-blue-500">
                    <RefreshCw className="h-3 w-3 animate-spin" />
                    Đang kết nối...
                </Badge>;
            case CallStatus.CONNECTED:
                return <Badge variant="default" className="flex items-center gap-1 bg-green-500">
                    <Phone className="h-3 w-3" />
                    Đang gọi
                </Badge>;
            case CallStatus.DISCONNECTED:
                return <Badge variant="outline" className="flex items-center gap-1 text-orange-500 border-orange-500">
                    <PhoneOff className="h-3 w-3" />
                    Đã kết thúc
                </Badge>;
            case CallStatus.INCOMING:
                return <Badge variant="default" className="flex items-center gap-1 bg-blue-500">
                    <PhoneIncoming className="h-3 w-3" />
                    Cuộc gọi đến
                </Badge>;
            case CallStatus.OUTGOING:
                return <Badge variant="default" className="flex items-center gap-1 bg-blue-500">
                    <PhoneOutgoing className="h-3 w-3" />
                    Đang gọi đi...
                </Badge>;
            case CallStatus.ERROR:
                return <Badge variant="destructive" className="flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    Lỗi
                </Badge>;
            default:
                return null;
        }
    };

    return (
        <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold flex items-center gap-2">
                    <PhoneCall className="h-5 w-5 text-primary" />
                    Giao diện gọi điện
                </CardTitle>
                <div className="flex items-center gap-2">
                    {renderCallStatus()}
                    {isInitialized ? (
                        <Badge variant="outline" className="flex items-center gap-1 text-green-500 border-green-500">
                            <CheckCircle2 className="h-3 w-3" />
                            Đã kết nối SIP
                        </Badge>
                    ) : (
                        <Badge variant="outline" className="flex items-center gap-1 text-red-500 border-red-500">
                            <XCircle className="h-3 w-3" />
                            Chưa kết nối SIP
                        </Badge>
                    )}
                </div>
            </CardHeader>

            <CardContent className="pt-4 space-y-4">
                {/* Thông tin cuộc gọi */}
                {callState.status !== CallStatus.IDLE && (
                    <Alert className="bg-blue-50 border-blue-200">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-blue-500" />
                                <AlertTitle className="text-blue-700">
                                    {callState.remoteIdentity || destination}
                                </AlertTitle>
                            </div>
                            <div className="flex items-center gap-1 text-blue-700">
                                <Clock className="h-4 w-4" />
                                <span className="font-mono">{formatDuration(callDuration)}</span>
                            </div>
                        </div>
                    </Alert>
                )}

                {/* Form nhập số điện thoại */}
                {(callState.status === CallStatus.IDLE || callState.status === CallStatus.DISCONNECTED) && (
                    <div className="space-y-2">
                        <label htmlFor="destination" className="text-sm font-medium flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Số điện thoại
                        </label>
                        <div className="flex gap-2">
                            <Input
                                id="destination"
                                value={destination}
                                onChange={(e) => setDestination(e.target.value)}
                                placeholder="Nhập số điện thoại"
                                className="flex-1"
                            />
                            <Button
                                onClick={() => handleCall()}
                                disabled={!isInitialized || !destination}
                                title="Gọi với microphone"
                            >
                                <PhoneOutgoing className="h-4 w-4 mr-2" />
                                Gọi
                            </Button>
                            <Button
                                variant="outline"
                                onClick={() => handleCall({ skipMediaAccess: true })}
                                disabled={!isInitialized || !destination}
                                title="Gọi mà không cần microphone (chỉ nghe)"
                            >
                                <Phone className="h-4 w-4 mr-2" />
                                Chỉ nghe
                            </Button>
                        </div>
                    </div>
                )}

                {/* Hiển thị gợi ý khi có lỗi microphone */}
                {callState.status === CallStatus.ERROR && callState.error?.includes('microphone') && (
                    <Alert variant="destructive" className="bg-red-50 border-red-200">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Lỗi truy cập microphone</AlertTitle>
                        <AlertDescription>
                            Bạn có thể sử dụng nút "Chỉ nghe" nếu không có microphone hoặc không muốn sử dụng microphone.
                        </AlertDescription>
                    </Alert>
                )}

                {/* Nút điều khiển cuộc gọi đến */}
                {callState.status === CallStatus.INCOMING && (
                    <div className="space-y-4">
                        <Alert className="bg-blue-50 border-blue-200">
                            <PhoneIncoming className="h-4 w-4 text-blue-500" />
                            <AlertTitle className="text-blue-700">
                                Cuộc gọi đến từ: {callState.remoteIdentity}
                            </AlertTitle>
                        </Alert>
                        <div className="flex justify-center gap-2">
                            <Button
                                variant="default"
                                className="bg-green-500 hover:bg-green-600"
                                onClick={() => handleAnswer()}
                                title="Trả lời với microphone"
                            >
                                <PhoneIncoming className="h-4 w-4 mr-2" />
                                Trả lời
                            </Button>
                            <Button
                                variant="outline"
                                className="text-green-500 border-green-500 hover:bg-green-50"
                                onClick={() => handleAnswer({ skipMediaAccess: true })}
                                title="Trả lời mà không cần microphone (chỉ nghe)"
                            >
                                <Phone className="h-4 w-4 mr-2" />
                                Chỉ nghe
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleReject}
                            >
                                <PhoneOff className="h-4 w-4 mr-2" />
                                Từ chối
                            </Button>
                        </div>
                    </div>
                )}

                {/* Nút điều khiển cuộc gọi đang diễn ra */}
                {(callState.status === CallStatus.CONNECTED ||
                  callState.status === CallStatus.OUTGOING) && (
                    <div className="flex justify-center gap-2">
                        <Button
                            variant="destructive"
                            onClick={handleHangup}
                        >
                            <PhoneOff className="h-4 w-4 mr-2" />
                            Kết thúc
                        </Button>
                        <Button
                            variant={isMuted ? "outline" : "secondary"}
                            className={isMuted ? "border-orange-500 text-orange-500 hover:bg-orange-50" : ""}
                            onClick={handleToggleMute}
                        >
                            {isMuted ? <MicOff className="h-4 w-4 mr-2" /> : <Mic className="h-4 w-4 mr-2" />}
                            {isMuted ? 'Bật mic' : 'Tắt mic'}
                        </Button>
                        <Button
                            variant={callState.recording ? "outline" : "secondary"}
                            className={callState.recording ? "border-red-500 text-red-500 hover:bg-red-50" : ""}
                            onClick={handleRecording}
                            disabled={callState.status !== CallStatus.CONNECTED}
                        >
                            <Disc className="h-4 w-4 mr-2" />
                            {callState.recording ? 'Dừng ghi âm' : 'Ghi âm'}
                        </Button>
                    </div>
                )}

                {/* Hiển thị lỗi nếu có */}
                {callState.status === CallStatus.ERROR && callState.error && (
                    <Alert variant="destructive" className="mt-4">
                        <div className="flex justify-between items-center w-full">
                            <div className="flex items-center gap-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>Lỗi: {callState.error}</AlertTitle>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => initialize(username, password)}
                                title="Thử kết nối lại"
                                className="border-white text-white hover:bg-red-600"
                            >
                                <RefreshCw className="h-3 w-3 mr-1" />
                                Thử lại
                            </Button>
                        </div>
                        <AlertDescription className="mt-2">
                            <p className="font-medium">Nguyên nhân có thể:</p>
                            <ul className="list-disc list-inside text-sm mt-1 space-y-1">
                                <li>Máy chủ SIP không hoạt động hoặc không thể truy cập</li>
                                <li>Thông tin đăng nhập không chính xác</li>
                                <li>Vấn đề về mạng hoặc tường lửa</li>
                            </ul>
                        </AlertDescription>
                    </Alert>
                )}
            </CardContent>
        </Card>
    );
};

export default CallInterface;
