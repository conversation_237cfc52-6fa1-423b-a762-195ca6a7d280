import React, { useState } from 'react';
import CallInterface from '../components/CallInterface';
import { SIP_CONFIG } from '../config/sipConfig';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { PageHeader } from '@/components/common/PageHeader';
import {
  PhoneCall,
  Phone,
  PhoneIncoming,
  PhoneOff,
  Mic,
  MicOff,
  Disc,
  Info,
  User,
  Lock
} from 'lucide-react';

const CallCenterPage: React.FC = () => {
    const [username, setUsername] = useState(SIP_CONFIG.DEFAULT_USER);
    const [password, setPassword] = useState(SIP_CONFIG.DEFAULT_PASSWORD);
    const [isConfigured, setIsConfigured] = useState(false);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsConfigured(true);
    };

    return (
        <div className="space-y-6">
            <PageHeader
                title="Trung tâm cuộc gọi"
                description="Quản lý cuộc gọi khách hàng qua WebRTC và SIP"
            />

            <div className="grid gap-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                    {/* Phần chính */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-5">
                        {!isConfigured ? (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <PhoneCall className="h-5 w-5" />
                                        Cấu hình tài khoản SIP
                                    </CardTitle>
                                    <CardDescription>
                                        Nhập thông tin đăng nhập để kết nối với máy chủ SIP
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="username" className="flex items-center gap-2">
                                                <User className="h-4 w-4" />
                                                Tên đăng nhập
                                            </Label>
                                            <Input
                                                id="username"
                                                value={username}
                                                onChange={(e) => setUsername(e.target.value)}
                                                required
                                            />
                                            <p className="text-sm text-muted-foreground">
                                                Tài khoản mặc định: e1, e2, webrtc1000
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="password" className="flex items-center gap-2">
                                                <Lock className="h-4 w-4" />
                                                Mật khẩu
                                            </Label>
                                            <Input
                                                type="password"
                                                id="password"
                                                value={password}
                                                onChange={(e) => setPassword(e.target.value)}
                                                required
                                            />
                                            <p className="text-sm text-muted-foreground">
                                                Mật khẩu mặc định: Xproz2025 (cho e1, e2), abc123 (cho webrtc1000)
                                            </p>
                                        </div>
                                        <Button type="submit" className="w-full">
                                            <PhoneCall className="mr-2 h-4 w-4" />
                                            Kết nối
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        ) : (
                            <CallInterface username={username} password={password} />
                        )}
                    </div>

                    {/* Phần hướng dẫn */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Info className="h-5 w-5" />
                                    Hướng dẫn sử dụng
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h3 className="font-medium flex items-center gap-2 mb-2">
                                        <PhoneCall className="h-4 w-4" />
                                        Thực hiện cuộc gọi
                                    </h3>
                                    <ol className="list-decimal list-inside space-y-1 text-sm">
                                        <li>Nhập số điện thoại vào ô "Số điện thoại"</li>
                                        <li>Nhấn nút "Gọi" để bắt đầu cuộc gọi</li>
                                        <li>Đợi đến khi cuộc gọi được kết nối</li>
                                        <li>Sử dụng các nút điều khiển để tắt/bật mic, ghi âm, hoặc kết thúc cuộc gọi</li>
                                    </ol>
                                </div>

                                <div>
                                    <h3 className="font-medium flex items-center gap-2 mb-2">
                                        <PhoneIncoming className="h-4 w-4" />
                                        Trả lời cuộc gọi đến
                                    </h3>
                                    <ol className="list-decimal list-inside space-y-1 text-sm">
                                        <li>Khi có cuộc gọi đến, bạn sẽ thấy thông báo "Cuộc gọi đến"</li>
                                        <li>Nhấn nút "Trả lời" để nhận cuộc gọi hoặc "Từ chối" để từ chối</li>
                                    </ol>
                                </div>

                                <div>
                                    <h3 className="font-medium flex items-center gap-2 mb-2">
                                        <Phone className="h-4 w-4" />
                                        Các số máy nhánh
                                    </h3>
                                    <ul className="list-disc list-inside space-y-1 text-sm">
                                        <li><strong>1</strong> - Extension e1</li>
                                        <li><strong>2</strong> - Extension e2</li>
                                        <li><strong>6000</strong> - demo-abouttotry</li>
                                        <li><strong>jack</strong> - jack-vietnam-2</li>
                                        <li><strong>6001</strong> - WebRTC call with recording</li>
                                    </ul>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CallCenterPage;
