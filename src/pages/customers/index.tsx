import { DefaultLayout } from "@/components/layout/DefaultLayout";
import { SEO } from "@/components/SEO";
import { Users, Plus } from "lucide-react";

// Import common components
import { PageHeader, PageHeaderAction } from "@/components/common/PageHeader";
import { EmptyState } from "@/components/common/EmptyState";

export default function Customers() {
  return (
    <DefaultLayout>
      <SEO title="Khách hàng" description="Quản lý danh sách khách hàng" />
      <div className="space-y-6">
        <PageHeader
          title="Khách hàng"
          description="Quản lý danh sách khách hàng"
          actions={
            <PageHeaderAction
              icon={<Plus className="h-4 w-4" />}
              text="Thêm khách hàng"
              onClick={() => {}}
            />
          }
        />

        <EmptyState
          title="Chưa có dữ liệu khách hàng"
          description="Bắt đầu thêm khách hàng mới vào hệ thống"
          icon={<Users className="h-6 w-6" />}
          actionText="Thêm khách hàng"
          onAction={() => {}}
          withBorder={true}
          size="lg"
        />
      </div>
    </DefaultLayout>
  );
}
