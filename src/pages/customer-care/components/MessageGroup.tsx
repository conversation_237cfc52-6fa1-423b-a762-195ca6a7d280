import React from "react";
import {Badge} from "@/components/ui/badge.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {cn, formatTime } from '@/lib/utils';
import {CheckCheck} from "lucide-react";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar.tsx";

interface ChatContentProps {
  groupName: string
  messages: Message[]
  showAvatar: boolean
  userId: string
  conversation: Conversation
}

const MessageGroup: React.FC<ChatContentProps> = ({
                                                    groupName,
                                                    messages,
                                                    showAvatar,
                                                    userId,
                                                    conversation,
                                                  }) => {


  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (<div>
    <div className="flex items-center justify-center my-4">
      <Badge variant="outline" className="bg-slate-800 text-slate-300 border-slate-600 text-xs">
        {groupName}
      </Badge>
    </div>

    {/* Messages */}
    {messages.map((msg) => {
      const isSender = userId === msg.senderId
      const sender = conversation?.users[msg.senderId]
      return <div
        key={msg._id}
                  className={`flex gap-3 my-2 ${isSender ? 'justify-end' : 'justify-start'}`}
      >
        {showAvatar && !isSender && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarImage
              src={sender?.avartar || undefined}
              alt={sender?.fullName}
            />
            <AvatarFallback className="bg-muted text-muted-foreground text-xs">
              {getInitials(sender?.fullName)}
            </AvatarFallback>
          </Avatar>
        )}
        <div
          className={`max-w-[70%] px-3 py-2 ${
            isSender
              ? 'bg-purple-600 text-white rounded-l-xl rounded-tr-xl'
              : 'bg-slate-700 text-slate-100 rounded-r-xl rounded-tl-xl'
          }`}
        >
          <p className={cn("text-sm whitespace-pre-wrap break-words",
            (!isSender && !msg.read) && "font-medium"
          )}>
            {msg.text}
          </p>

          <div
            className="flex items-center gap-1 mt-1"
          >
            <span className={`text-xs ${
              isSender ? 'text-purple-200' : 'text-slate-400'
            }`}>
             {formatTime(msg.createdAt)}
            </span>

            {msg.read && isSender && (
              <span className="text-xs h-4 px-1">
                <CheckCheck size={16}/>
              </span>
            )}
          </div>

        </div>

        {showAvatar && isSender && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarImage
              src={sender?.avartar || undefined}
              alt={sender?.fullName}
            />
            <AvatarFallback className="bg-muted text-muted-foreground text-xs">
              {getInitials(sender?.fullName)}
            </AvatarFallback>
          </Avatar>
        )}

      </div>
    })}
  </div>)

}

export default MessageGroup;