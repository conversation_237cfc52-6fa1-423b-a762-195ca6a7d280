import React, {useState, useRef} from 'react';
import {Button} from '@/components/ui/button';
import {Conversation} from '@/services/CskhService';
import {ChatStatusSelector} from './ChatStatusSelector';
import {Bot, Circle, Clock, X} from 'lucide-react';

export type ChatStatus = 'open' | 'close' | 'bot';

interface ChatStatusButtonProps {
  status: ChatStatus;
  conversation: Conversation;
  onStatusChange?: (status: ChatStatus) => void;
  disabled?: boolean;
}

const statusConfig = {
  open: {
    label: 'Mở',
    color: 'bg-green-500 hover:bg-green-600',
    icon: <Circle size={14}/>,
  },
  close: {
    label: 'Đóng',
    color: 'bg-red-500 hover:bg-red-600',
    icon: <X size={14}/>,
  },
  waiting: {
    label: 'Chờ kết nối',
    color: 'bg-yellow-500 hover:bg-yellow-600',
    icon: <Clock size={14}/>,
  },
  chatbot: {
    label: 'Chat Bot',
    color: 'bg-blue-500 hover:bg-blue-600',
    icon: <Bot size={14}/>,
  }
};

export const ChatStatusButton: React.FC<ChatStatusButtonProps> = ({
                                                                    status = 'close',
                                                                    conversation,
                                                                    onStatusChange,
                                                                    disabled = false,
                                                                  }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [popupPosition, setPopupPosition] = useState<'left' | 'right'>('left');
  const buttonRef = useRef<HTMLDivElement>(null);

  const handleStatusChange = (newStatus: ChatStatus) => {
    setIsOpen(false);
    onStatusChange?.(newStatus);
  };

  const calculatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const popupWidth = 288; // w-72 = 288px
      const screenWidth = window.innerWidth;
      return rect.left + popupWidth > screenWidth - 20 ? 'right' : 'left';
    }
    return 'left';
  };

  const handleTogglePopup = () => {
    if (!isOpen) {
      const newPosition = calculatePosition();
      setPopupPosition(newPosition);
    }
    setIsOpen(!isOpen);
  };

  const currentConfig = statusConfig[status];

  return (
    <div ref={buttonRef} className="relative inline-block">
      <Button
        onClick={handleTogglePopup}
        disabled={disabled}
        className={`${currentConfig.color} text-white transition-all duration-200 h-8 px-3 text-sm flex items-center gap-2`}
      >
        {currentConfig.icon}
        <span>{currentConfig.label}</span>
      </Button>

      {isOpen && (
        <ChatStatusSelector
          currentStatus={status}
          onStatusSelect={handleStatusChange}
          onClose={() => setIsOpen(false)}
          position={popupPosition}
        />
      )}
    </div>
  );
};