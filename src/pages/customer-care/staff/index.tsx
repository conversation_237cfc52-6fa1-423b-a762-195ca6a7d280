import {DefaultLayout} from "@/components/layout/DefaultLayout";
import React, {useCallback, useEffect, useState, useRef} from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {useDebouncedSearch} from '@/hooks/use-debounced-search'
import {Conversation, cskhService, Message} from '@/services/CskhService';
import {SEO} from "@/components/SEO";
import ChatSidebar from '@/pages/customer-care/staff/ChatSidebar.tsx';
import ChatHeader from '@/pages/customer-care/staff/ChatHeader.tsx';
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx';
import ChatInput from '@/pages/customer-care/components/ChatInput.tsx';
import {toast} from '@/hooks/use-toast';
import useSocket from "@/hooks/use-socket.ts";

export default function ChatStaff() {

  const {user} = useAuth();
  const [activeValue, setActiveValue] = useState('open');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const {socket, socketConnected} = useSocket(user._id);

  const activeValueRef = useRef(activeValue);

  useEffect(() => {
    activeValueRef.current = activeValue;
  }, [activeValue]);

  // Initialize socket connection
  useEffect(() => {
    if (!socket) return;


    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      });
    });

    socket.on("notify_conversation", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
    });


    socket.on("update_conversation", (data) => {
      if (data.room.status !== activeValueRef) {
        setConversations(prevState => {
          return prevState.filter(e => e._id !== data.room._id)
        })
        setMessages([])
        setActiveConversationId(null)
      } else {
        setConversations(prevState =>
          prevState.map(item =>
            item._id === data.room._id ? {...item, ...data.room} : item
          )
        );
      }

    });

    socket.on("read_message", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
      setMessages(data.messages);
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    fetchConversations();
  }, [activeValue]);


  const fetchConversations = async () => {
    try {
      setLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 100,
        userId: user._id,
        title: searchQuery,
        status: activeValue
      };

      const result = await cskhService.getConversations(params);
      if (result) {
        if (activeConversationId)
          socket.emit("leave_room", {room_id: activeConversationId});
        setConversations(result.data);
        setActiveConversationId(null)
        setMessages([])
      }

    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách cuộc trò chuyện",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchMessages = async (conversationId) => {
    try {
      setMessagesLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 100,
      };

      const result = await cskhService.getMessages(conversationId, params);
      if (result) {
        setMessages(result.data.messages);
      }

    } catch (error) {
      console.error('Error fetching message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải nội dung tin nhắn",
        variant: "destructive",
      });
    } finally {
      setMessagesLoading(false);
    }
  };


  const serchConversations = useCallback(() => {
    fetchConversations()
  }, [searchQuery]);

  useDebouncedSearch(searchQuery, 500, serchConversations);

  const handleSelectConversation = async (conversation: Conversation) => {
    if (activeConversationId === conversation._id) return;
    if (activeConversationId) socket.emit("leave_room", {room_id: activeConversationId});

    socket.emit("join_room", {room_id: conversation._id});
    setActiveConversationId(conversation._id);
    if (conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
    else
      await fetchMessages(conversation._id)

  };

  const handleFocus = async () => {
    const activeConversation = conversations?.find(conv => conv._id === activeConversationId);
    if (activeConversation.unread > 0)
      await cskhService.markReadMessage(activeConversationId);
  }

  const handleSendMessage = async (body, room_id = activeConversationId) => {
    if (!room_id || !user) return;

    try {
      await cskhService.sendMessages(room_id, body)
    } catch (error) {
      console.error('❌ Error sending message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn",
        variant: "destructive",
      });
    }
  };

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);


  return (
    <DefaultLayout>
      <SEO title="Chat với khách hàng" description="Tương tác với khách hàng qua hệ thống chat thời gian thực"/>
      <div className="space-y-6">
        <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">
          {/* Sidebar */}
          <ChatSidebar
            activeValue={activeValue}
            setActiveValue={setActiveValue}
            conversations={conversations}
            activeConversationId={activeConversationId}
            onSelectConversation={handleSelectConversation}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            loading={loading}
          />

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <ChatHeader
              conversation={activeConversation || null}
              disabled={!activeConversationId || activeConversation?.status === 'close'}
              onViewProfile={() => {
                // TODO: Navigate to customer profile
                toast({
                  title: "Tính năng đang phát triển",
                  description: "Tính năng xem thông tin khách hàng sẽ được tích hợp",
                });
              }}
            />

            {/* Messages */}
            <ChatContent
              messages={messages}
              conversation={activeConversation || null}
              showAvatar={true}
            />

            {/* Input */}
            {activeConversationId && (
              <ChatInput
                handleFocus={handleFocus}
                handleSendMessage={handleSendMessage}
                disabled={!activeConversationId || activeConversation?.status === 'close'}
                placeholder="Nhấn Enter để gửi, Shift + Enter để xuống dòng"
              />
            )}

            {/* Socket status indicator */}
            {!socketConnected && (
              <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200 text-yellow-800 text-sm">
                ⚠️ Chế độ offline - Tin nhắn sẽ được gửi nhưng không có real-time updates
              </div>
            )}
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}
