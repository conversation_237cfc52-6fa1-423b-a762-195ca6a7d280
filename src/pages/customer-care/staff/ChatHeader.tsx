import React from 'react';
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar';
import {Button} from '@/components/ui/button';
import {MoreVertical, User} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {Conversation, cskhService} from '@/services/CskhService';
import {useAuth} from "@/contexts/AuthContext";
import {ChatStatusButton} from './StatusUpdata';
import {toast} from "@/hooks/use-toast.ts";

interface ChatHeaderProps {
  disabled?: boolean;
  conversation: Conversation | null;
  onViewProfile?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 disabled,
                                                 conversation,
                                                 onViewProfile,
                                               }) => {
  const {user} = useAuth();

  if (!conversation) {
    return (
      <div className="h-16 border-b border-border bg-card flex items-center justify-center">
        <p className="text-muted-foreground"><PERSON><PERSON><PERSON> một cuộc trò chuyện để bắt đầu</p>
      </div>
    );
  }

  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleStatusChange = async (newStatus) => {
    const room_id = conversation._id
    const body_update = {status: newStatus}
    const api = await cskhService.updateConversation(room_id, body_update)
    if (api)
      toast({
        title: "Thành công",
        description: "Cập nhật trạng thái cuộc trò truyện thành công",
      });
  };

  return (
    <div className="h-16 border-b border-border bg-card flex items-center justify-between px-4">
      {/* User Info */}
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage
            src={conversation.ownerUserId ? `/api/files/content/${conversation.ownerUserId}` : undefined}
            alt={conversation.title}
          />
          <AvatarFallback className="bg-primary/10 text-primary">
            {getInitials(conversation.title)}
          </AvatarFallback>
        </Avatar>

        <div>
          <h3 className="font-semibold text-sm">{conversation.title}</h3>
          <div className="flex items-center gap-2">
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <ChatStatusButton
          conversation={conversation}
          status={conversation.status}
          disabled={disabled}
          onStatusChange={handleStatusChange}
        />


        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-4 w-4"/>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onViewProfile && (
              <DropdownMenuItem onClick={onViewProfile}>
                <User className="h-4 w-4 mr-2"/>
                Xem thông tin khách hàng
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default ChatHeader;