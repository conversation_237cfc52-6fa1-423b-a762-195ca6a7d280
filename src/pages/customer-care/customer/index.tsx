import React, {useState, useEffect} from 'react';
import {MessageCircleQuestion, X} from 'lucide-react';
import {cskhService, Message, Conversation} from '@/services/CskhService';
import ChatHeader from '@/pages/customer-care/customer/ChatHeader.tsx'
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx'
import ChatInput from '@/pages/customer-care/components/ChatInput.tsx'
import {useAuth} from "@/contexts/AuthContext.tsx";
import {toast} from "@/hooks/use-toast.ts";
import {Badge} from "@/components/ui/badge.tsx";
import useSocket from "@/hooks/use-socket.ts";

export default function ChatCustomer() {
  const {user} = useAuth();
  const [conversation, setConversation] = useState<Conversation>(null);
  const [conversationId, setConversationId] = useState<string>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const {socket, socketConnected} = useSocket(user._id);

  useEffect(() => {
    const getData = async () => {
      try {
        const api = await cskhService.getCustomerConversations();

        if (api?.room && api?.messages) {
          setConversation(api.room);
          setConversationId(api.room._id);
          setMessages(api.messages);
        } else {
          console.warn("Không tìm thấy room hoặc messages.");
        }
      } catch (error) {
        console.error("Lỗi khi gọi API getCustomerConversations:", error);
      }
    }

    getData()
  }, []);


  useEffect(() => {
    if (!socket) return;

    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      });
    });

    socket.on("notify_conversation", (data) => {
      setConversation(data.room)
      setConversationId(data.room._id)
    });

    socket.on("update_conversation", (data) => {
      setConversation(data.room)
      setConversationId(data.room._id)
    });

    socket.on("read_message", (data) => {
      setConversation(data.room)
      setMessages(data.messages);
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    if (conversationId) {
      getMessages().then();
      socket.emit("join_room", {room_id: conversationId});
    }
  }, [conversationId]);


  const getMessages = async () => {
    const params = {
      pagination: true,
      page: 1,
      limit: 100,
    };

    const result = await cskhService.getMessages(conversationId, params);
    if (result) setMessages(result.data.messages)

  }


  const handleSendMessage = async (body, room_id = conversationId) => {
    if (!user) return;
    try {
      if (!room_id) {
        const api = await cskhService.createConversations()
        if (api) {
          setConversationId(api.data._id)
          room_id = api.data._id
        }
      }
      await cskhService.sendMessages(room_id, body)

    } catch (error) {
      console.error('❌ Error sending message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn",
        variant: "destructive",
      });
    }
  }

  const handleFocus = async () => {
    if (conversation && conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
  }

  const openMessageDialog = async () => {
    setIsOpen(!isOpen);
  }

  if (user?.role !== "user") return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isOpen && (
        <div className="relative mb-2">

          <div className="w-[450px] h-[600px] bg-slate-900 shadow-xl rounded-lg border border-slate-700 flex flex-col">
            <ChatHeader
              setIsOpen={setIsOpen}
              conversation={conversation}
            />

            <ChatContent
              messages={messages}
              conversation={conversation}

            />

            <ChatInput
              handleFocus={handleFocus}
              disabled={conversation?.status === 'close'}
              handleSendMessage={handleSendMessage}
            />
          </div>

          <div className="absolute -bottom-2 right-3">
            <svg
              width="24"
              height="12"
              viewBox="0 0 24 12"
              className="text-slate-900 drop-shadow-lg"
            >
              <path
                d="M12 12 L0 0 L24 0 Z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      )}
      <div className="flex justify-end mt-4">
        <button
          onClick={openMessageDialog}
          className="bg-purple-600 text-white p-3 rounded-full shadow-lg hover:bg-purple-700 transition-colors hover:scale-105"
        >
          {isOpen ? <X size={24}/> : <MessageCircleQuestion size={24}/>}
        </button>

        {conversation && conversation.unread > 0 && !isOpen && (
          <Badge
            variant="destructive"
            className="absolute top-2 right h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {conversation.unread > 99 ? '99+' : conversation.unread}
          </Badge>
        )}

      </div>
    </div>
  );
}
