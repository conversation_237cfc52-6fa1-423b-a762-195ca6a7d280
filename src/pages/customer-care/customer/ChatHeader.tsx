import React from "react";
import {Avatar, AvatarFallback} from "@/components/ui/avatar.tsx";
import {cn} from '@/lib/utils';
import {Conversation} from "@/services/CskhService.ts";
import {X} from "lucide-react";

interface ChatHeaderProps {
  setIsOpen: (open: boolean) => void;
  conversation: Conversation;
}

const configStatus = {
  open: {
    bg_color: 'bg-green-500',
    title: "Đã kết nối"
  },
  waiting: {
    bg_color: 'bg-yellow-500',
    title: "Chờ kết nối"
  },
  chatbot: {
    bg_color: 'bg-blue-500',
    title: "Tin nhắn tự động"
  },
  close: {
    bg_color: 'bg-red-500',
    title: "Kết thúc trò chuyện"
  },
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 setIsOpen,
                                                 conversation,
                                               }) => {

  const status = conversation?.status

  return (<div
      className="flex justify-between items-center p-4 border-b border-slate-700 bg-slate-800 text-white rounded-t-lg">
      <div className="flex items-center gap-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="bg-purple-600 text-white text-xs">
            SP
          </AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-lg font-semibold">Hỗ trợ trực tuyến</h2>
          {conversation && <p className="text-xs text-slate-300">
              <span className={cn(`${configStatus?.[status]?.bg_color}`,
                "inline-block w-2 h-2 rounded-full mr-1")}/>
            {configStatus?.[status]?.title}
          </p>}
        </div>
      </div>
      <button
        onClick={() => setIsOpen(false)}
        className="hover:bg-slate-700 p-1 rounded transition-colors"
      >
        <X size={20}/>
      </button>
    </div>
  )

}

export default ChatHeader;