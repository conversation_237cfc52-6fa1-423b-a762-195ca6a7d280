import { Route, Navigate } from "react-router-dom";
import { PrivateRoute } from "@/components/auth/PrivateRoute";
import { RoleBasedRoute } from "@/components/auth/RoleBasedRoute";

// Dashboard Pages
import Dashboard from "@/pages/dashboard";
import Call<PERSON>enter from "@/pages/call-center";
import Admins from "@/pages/admins";
import Customers from "@/pages/customers";
import Chat from "@/pages/chat";
import ChatCustomers from "@/pages/customer-care/staff";
import ProfilePage from "@/pages/profile";

export const ProtectedRoutes = (
  <Route element={<PrivateRoute />}>
    {/* Admin/Staff only routes */}
    <Route element={<RoleBasedRoute allowedRoles={["admin", "staff"]} redirectTo="/dashboard" />}>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/call-center" element={<CallCenter />} />
      <Route path="/admins" element={<Admins />} />
      <Route path="/customers" element={<Customers />} />
      <Route path="/chat" element={<Chat />} />
      <Route path="/customers-care" element={<ChatCustomers />} />
    </Route>

    {/* Common routes for all authenticated users */}
    <Route path="/profile" element={<ProfilePage />} />
  </Route>
);
