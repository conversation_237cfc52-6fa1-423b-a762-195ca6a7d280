import { api } from "@/lib/api";

/**
 * Interface cho dữ liệu người dùng
 */
export interface User {
  _id: string;
  fullName: string;
  email: string;
  role: string;
  avatarId?: string;
  phone?: string;
  gender?: string;
  status?: string;
}

/**
 * Interface cho dữ liệu đăng nhập
 */
export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Interface cho kết quả đăng nhập
 */
export interface LoginResponse {
  user: User;
  message: string;
}

/**
 * Interface cho dữ liệu cập nhật profile
 */
export interface ProfileUpdateData {
  fullName?: string;
  email?: string;
  phone?: string;
  gender?: string;
  status?: string;
  avatarId?: string;
}

/**
 * Interface cho dữ liệu tạo mới người dùng
 */
export interface CreateUserData {
  fullName: string;
  email: string;
  password: string;
  phone?: string;
  gender?: string;
  role?: string;
  status?: string;
}

/**
 * Interface cho dữ liệu cập nhật người dùng
 */
export interface UpdateUserData {
  fullName?: string;
  email?: string;
  phone?: string;
  gender?: string;
  role?: string;
  status?: string;
  password?: string;
}

/**
 * Interface cho dữ liệu thay đổi mật khẩu
 */
export interface ChangePasswordData {
  oldPassword: string;
  newPassword: string;
}

/**
 * Interface cho tham số tìm kiếm người dùng
 */
export interface UserSearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  role?: string;
  status?: string;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  rows: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * UserService - Quản lý các API liên quan đến người dùng
 */
class UserService {
  /**
   * Đăng nhập người dùng
   * @param email Email đăng nhập
   * @param password Mật khẩu
   * @returns Thông tin người dùng sau khi đăng nhập
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    return api.post<LoginResponse>('/users/login', { email, password });
  }

  /**
   * Lấy thông tin người dùng hiện tại
   * @returns Thông tin người dùng
   */
  async getCurrentUser(): Promise<User> {
    return api.get<User>('/users/me');
  }

  /**
   * Đăng xuất người dùng
   */
  async logout(): Promise<void> {
    await api.post('/users/logout');
  }

  /**
   * Gửi yêu cầu quên mật khẩu
   * @param email Email của người dùng
   */
  async forgotPassword(email: string): Promise<{ message: string }> {
    return api.post<{ message: string }>('/users/forgot-password', { email });
  }

  /**
   * Đặt lại mật khẩu
   * @param token Token xác thực
   * @param password Mật khẩu mới
   */
  async resetPassword(token: string, password: string): Promise<{ message: string }> {
    return api.post<{ message: string }>('/users/reset-password', { token, password });
  }

  /**
   * Cập nhật thông tin người dùng
   * @param userId ID người dùng
   * @param userData Dữ liệu cập nhật
   */
  async updateProfile(profileData: ProfileUpdateData): Promise<User> {
    // Chuyển đổi dữ liệu từ form sang định dạng API
    const userData: Partial<User> = {
      fullName: profileData.fullName,
      email: profileData.email,
      phone: profileData.phone,
      gender: profileData.gender,
      avatarId: profileData.avatarId
    };

    return api.put<User>('/users/profile', userData);
  }

  /**
   * Thay đổi mật khẩu
   * @param currentPassword Mật khẩu hiện tại
   * @param newPassword Mật khẩu mới
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<{ message: string }> {
    return api.post<{ message: string }>('/users/changePassword', {
      oldPassword,
      newPassword
    });
  }

  /**
   * Tải lên avatar
   * @param file File ảnh
   */
  async uploadAvatar(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('avatar', file);

    // Sử dụng api.post nhưng không stringify body vì đây là FormData
    return api.post<{ url: string }>('/users/avatar', formData, {
      headers: {
        'Content-Type': undefined // Để browser tự động thiết lập Content-Type cho FormData
      }
    });
  }

  /**
   * Lấy danh sách tất cả người dùng có phân trang
   * @param params Tham số tìm kiếm và phân trang
   * @returns Kết quả phân trang với danh sách người dùng
   */
  async getAllUsers(params: UserSearchParams = {}): Promise<PaginatedResult<User>> {
    return api.get<PaginatedResult<User>>('/users', { params: params as Record<string, string> });
  }

  /**
   * Tạo mới người dùng
   * @param userData Dữ liệu người dùng mới
   * @returns Thông tin người dùng đã tạo
   */
  async createUser(userData: CreateUserData): Promise<User> {
    return api.post<User>('/users', userData);
  }

  /**
   * Cập nhật thông tin người dùng theo ID
   * @param userId ID người dùng
   * @param userData Dữ liệu cập nhật
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUser(userId: string, userData: UpdateUserData): Promise<User> {
    return api.put<User>(`/users/${userId}`, userData);
  }

  /**
   * Xóa người dùng
   * @param userId ID người dùng cần xóa
   * @returns Kết quả xóa
   */
  async deleteUser(userId: string): Promise<{ message: string }> {
    console.log("userId",userId)
    return api.delete<{ message: string }>(`/users/${userId}`);
  }
}

// Export instance của service
export const userService = new UserService();
