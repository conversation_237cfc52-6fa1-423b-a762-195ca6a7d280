import { api } from "@/lib/api";

/**
 * Interface cho metric trên dashboard
 */
export interface DashboardMetric {
  id: string;
  title: string;
  value: number | string;
  description: string;
  icon?: string;
  colorClass?: string;
}

/**
 * Interface cho dữ liệu tổng quan dashboard
 */
export interface DashboardSummary {
  metrics: DashboardMetric[];
  recentActivities: any[];
  charts: {
    callsByDay: any[];
    customersByMonth: any[];
  };
}

/**
 * DashboardService - Quản lý các API liên quan đến dashboard
 */
class DashboardService {
  /**
   * Lấy dữ liệu tổng quan cho dashboard
   * @returns Dữ liệu tổng quan dashboard
   */
  async getSummary(): Promise<DashboardSummary> {
    return api.get<DashboardSummary>('/dashboard/summary');
  }

  /**
   * Lấy dữ liệu metrics cho dashboard
   * @returns Danh sách metrics
   */
  async getMetrics(): Promise<DashboardMetric[]> {
    return api.get<DashboardMetric[]>('/dashboard/metrics');
  }

  /**
   * Lấy dữ liệu hoạt động gần đây
   * @param limit Số lượng hoạt động muốn lấy
   * @returns Danh sách hoạt động gần đây
   */
  async getRecentActivities(limit: number = 10): Promise<any[]> {
    return api.get<any[]>('/dashboard/activities', { params: { limit: limit.toString() } });
  }

  /**
   * Lấy dữ liệu biểu đồ cuộc gọi theo ngày
   * @param days Số ngày muốn lấy dữ liệu
   * @returns Dữ liệu biểu đồ
   */
  async getCallsByDay(days: number = 7): Promise<any[]> {
    return api.get<any[]>('/dashboard/charts/calls-by-day', { params: { days: days.toString() } });
  }

  /**
   * Lấy dữ liệu biểu đồ khách hàng theo tháng
   * @param months Số tháng muốn lấy dữ liệu
   * @returns Dữ liệu biểu đồ
   */
  async getCustomersByMonth(months: number = 6): Promise<any[]> {
    return api.get<any[]>('/dashboard/charts/customers-by-month', { params: { months: months.toString() } });
  }
}

// Export instance của service
export const dashboardService = new DashboardService();
