import { api } from "@/lib/api";

/**
 * Interface cho cuộc gọi
 */
export interface Call {
  id: string;
  customerId?: string;
  customerName?: string;
  customerPhone: string;
  adminId?: string;
  adminName?: string;
  status: 'missed' | 'answered' | 'ongoing' | 'completed';
  direction: 'inbound' | 'outbound';
  duration?: number;
  notes?: string;
  recordingUrl?: string;
  startTime: string;
  endTime?: string;
}

/**
 * Interface cho tham số tìm kiếm cuộc gọi
 */
export interface CallSearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: string;
  direction?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * CallCenterService - Quản lý các API liên quan đến call center
 */
class CallCenterService {
  /**
   * <PERSON><PERSON><PERSON> danh sách cuộc gọi có phân trang
   * @param params Tham số tìm kiếm và phân trang
   * @returns Kết quả phân trang với danh sách cuộc gọi
   */
  async getCalls(params: CallSearchParams = {}): Promise<PaginatedResult<Call>> {
    return api.get<PaginatedResult<Call>>('/calls', { params: params as Record<string, string> });
  }

  /**
   * Lấy thông tin chi tiết của một cuộc gọi
   * @param id ID của cuộc gọi
   * @returns Thông tin chi tiết cuộc gọi
   */
  async getCallById(id: string): Promise<Call> {
    return api.get<Call>(`/calls/${id}`);
  }

  /**
   * Tạo cuộc gọi mới (outbound)
   * @param phoneNumber Số điện thoại cần gọi
   * @returns Thông tin cuộc gọi đã tạo
   */
  async createCall(phoneNumber: string): Promise<Call> {
    return api.post<Call>('/calls', { phoneNumber, direction: 'outbound' });
  }

  /**
   * Cập nhật thông tin cuộc gọi
   * @param id ID của cuộc gọi
   * @param callData Dữ liệu cập nhật
   * @returns Thông tin cuộc gọi sau khi cập nhật
   */
  async updateCall(id: string, callData: Partial<Call>): Promise<Call> {
    return api.put<Call>(`/calls/${id}`, callData);
  }

  /**
   * Kết thúc cuộc gọi
   * @param id ID của cuộc gọi
   * @param notes Ghi chú cuộc gọi
   * @returns Thông tin cuộc gọi sau khi kết thúc
   */
  async endCall(id: string, notes?: string): Promise<Call> {
    return api.post<Call>(`/calls/${id}/end`, { notes });
  }

}

// Export instance của service
export const callCenterService = new CallCenterService();
