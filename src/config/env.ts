/**
 * <PERSON><PERSON><PERSON> hình môi trường cho ứng dụng
 * Tập trung quản lý tất cả các biến môi trường tại một nơi
 */

// Hàm helper để lấy biến môi trường với giá trị mặc định
const getEnvVariable = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue;
};

// Cấu hình API
export const API_CONFIG = {
  BASE_URL: getEnvVariable('VITE_API_BASE_URL', '/api'),
  BACKEND_URL: getEnvVariable('VITE_API_BACKEND_URL', 'http://localhost:3000'),
  UPLOAD_URL: getEnvVariable('VITE_UPLOAD_URL', '/upload/file'),
  FILE_CONTENT_URL: getEnvVariable('VITE_FILE_CONTENT_URL', '/api/files/content'),
  ENABLE_LOGS: getEnvVariable('VITE_ENABLE_API_LOGS', 'false') === 'true',
};

// Cấu hình SIP
export const SIP_CONFIG = {
  // SIP Server
  SERVER_URL: getEnvVariable('VITE_SIP_SERVER_URL', '***************:5090'),
  WEBSOCKET_URL: getEnvVariable('VITE_SIP_WEBSOCKET_URL', 'ws://***************:8088/ws'),

  // Tài khoản mặc định
  DEFAULT_USER: getEnvVariable('VITE_SIP_DEFAULT_USER', 'e1'),
  DEFAULT_PASSWORD: getEnvVariable('VITE_SIP_DEFAULT_PASSWORD', 'Xproz2025'),

  // Extensions
  EXTENSIONS: {
    E1: '1',
    E2: '2',
    DEMO: '6000',
    JACK: 'jack',
    RECORDING: '6001'
  },

  // Cấu hình khác
  MAIN_SERVER: getEnvVariable('VITE_SIP_MAIN_SERVER', '**************:5060'),
  BACKUP_SERVER: getEnvVariable('VITE_SIP_BACKUP_SERVER', '***************:5060'),
  SIGNALING_PORT: getEnvVariable('VITE_SIP_SIGNALING_PORT', '5060'),
  RTP_PORT_RANGE: getEnvVariable('VITE_SIP_RTP_PORT_RANGE', '10000-65535'),
  FROM_PREFIX: getEnvVariable('VITE_SIP_FROM_PREFIX', '09'),
  TO_PREFIX: getEnvVariable('VITE_SIP_TO_PREFIX', '9'),

  // Cấu hình STUN servers cho WebRTC
  ICE_SERVERS: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ]
};

// Cấu hình ứng dụng
export const APP_CONFIG = {
  NAME: getEnvVariable('VITE_APP_NAME', 'GHVN - Hệ thống quản trị'),
  DESCRIPTION: getEnvVariable('VITE_APP_DESCRIPTION', 'GHVN - Hệ thống quản trị'),
  KEYWORDS: getEnvVariable('VITE_APP_KEYWORDS', 'GHVN, quản trị, helpdesk, customer service'),
  LOGO: getEnvVariable('VITE_APP_LOGO', '/assets/images/logo/logo_ghvn.png'),
};

// Cấu hình môi trường
export const ENV_CONFIG = {
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
  MODE: import.meta.env.MODE,
};

// Export tất cả cấu hình
export default {
  API: API_CONFIG,
  SIP: SIP_CONFIG,
  APP: APP_CONFIG,
  ENV: ENV_CONFIG,
};
