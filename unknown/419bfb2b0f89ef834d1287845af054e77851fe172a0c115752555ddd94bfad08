import { Route } from "react-router-dom";
import { RoleBasedRoute } from "@/components/auth/RoleBasedRoute";

// Customer Pages
import CustomerDashboard from "@/pages/customer/CustomerDashboard";
import CustomerCall from "@/pages/customer/CustomerCall";
import CustomerChat from "@/pages/customer/CustomerChat";
import CustomerHistory from "@/pages/customer/CustomerHistory";
import ProfilePage from "@/pages/profile";

export const BuyerRoutes = (
  <Route element={<RoleBasedRoute allowedRoles={["buyer"]} redirectTo="/dashboard" />}>
    <Route path="/buyer/dashboard" element={<CustomerDashboard />} />
    <Route path="/buyer/call" element={<CustomerCall />} />
    <Route path="/buyer/support" element={<CustomerChat />} />
    <Route path="/buyer/history" element={<CustomerHistory />} />
    <Route path="/buyer/profile" element={<ProfilePage />} />
  </Route>
);
