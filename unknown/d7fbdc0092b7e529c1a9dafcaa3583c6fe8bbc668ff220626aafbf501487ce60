import { Routes } from "react-router-dom";
import { AuthRoutes } from "./authRoutes";
import { ProtectedRoutes } from "./protectedRoutes";
import { ShopRoutes } from "./shopRoutes";
import { BuyerRoutes } from "./buyerRoutes";
import { PublicRoutes } from "./publicRoutes";

export const AppRoutes = () => {
  return (
    <Routes>
      {/* Auth Routes */}
      {AuthRoutes}

      {/* Protected Routes */}
      {ProtectedRoutes}

      {/* Shop Routes */}
      {ShopRoutes}

      {/* Buyer Routes */}
      {BuyerRoutes}

      {/* Public Routes */}
      {PublicRoutes}
    </Routes>
  );
};
