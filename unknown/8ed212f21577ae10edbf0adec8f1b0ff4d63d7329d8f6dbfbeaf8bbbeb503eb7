import { Route, Navigate } from "react-router-dom";
import NotFound from "@/pages/NotFound";
import CustomerDemo from "@/pages/demo/CustomerDemo";

export const PublicRoutes = [
  // Redirect root to dashboard
  <Route key="root" path="/" element={<Navigate to="/dashboard" replace />} />,

  // Demo route
  <Route key="demo" path="/demo" element={<CustomerDemo />} />,

  // 404 route
  <Route key="not-found" path="*" element={<NotFound />} />,
];
